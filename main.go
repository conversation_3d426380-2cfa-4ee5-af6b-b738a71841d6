package main

import (
	"fmt"
	"log"

	"protobuf-example/gen"

	"google.golang.org/protobuf/proto"
)

func main() {
	fmt.Println("=== Protocol Buffer の使い方 ===")

	// 1. HelloRequest メッセージを作成
	request := &gen.HelloRequest{
		Name: "太郎",
	}
	fmt.Printf("作成したリクエスト: %v\n", request)
	fmt.Printf("名前: %s\n", request.GetName())

	// 2. バイナリ形式にシリアライズ（データを送信可能な形式に変換）
	data, err := proto.Marshal(request)
	if err != nil {
		log.Fatalf("シリアライズに失敗: %v", err)
	}
	fmt.Printf("シリアライズ後のデータサイズ: %d バイト\n", len(data))
	fmt.Printf("バイナリデータ: %x\n", data)

	// 3. バイナリからデシリアライズ（元のメッセージに復元）
	var newRequest gen.HelloRequest
	err = proto.Unmarshal(data, &newRequest)
	if err != nil {
		log.Fatalf("デシリアライズに失敗: %v", err)
	}
	fmt.Printf("復元されたリクエスト: %v\n", &newRequest)
	fmt.Printf("復元された名前: %s\n", newRequest.GetName())

	// 4. HelloReply メッセージを作成
	reply := &gen.HelloReply{
		Message: fmt.Sprintf("こんにちは、%sさん！", request.GetName()),
	}
	fmt.Printf("作成したレスポンス: %v\n", reply)
	fmt.Printf("メッセージ: %s\n", reply.GetMessage())

	// 5. レスポンスもシリアライズ・デシリアライズ
	replyData, err := proto.Marshal(reply)
	if err != nil {
		log.Fatalf("レスポンスのシリアライズに失敗: %v", err)
	}

	var newReply gen.HelloReply
	err = proto.Unmarshal(replyData, &newReply)
	if err != nil {
		log.Fatalf("レスポンスのデシリアライズに失敗: %v", err)
	}

	fmt.Printf("復元されたメッセージ: %s\n", newReply.GetMessage())
	fmt.Println("=== 完了 ===")
}
